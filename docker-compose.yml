services:
  php:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    environment:
      SERVER_NAME: "http://"
    ports:
      - "8800:80"
      - "5173:5173"
    volumes:
      - ./:/app
      - php_caddy_data:/data
      - php_caddy_config:/config
    tty: true
    healthcheck:
      test: [ "CMD-SHELL", "supervisorctl status || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    depends_on:
      db:
        condition: service_healthy
    networks:
      - default

  db:
    image: mariadb:11.4.5
    environment:
      MARIADB_ROOT_PASSWORD: ${DB_PASSWORD}
    ports:
      - "6603:3306"
    volumes:
      - db:/var/lib/mysql
      - ./docker/db/init/:/docker-entrypoint-initdb.d
    healthcheck:
      test: [ "CMD", "mariadb", "-u", "root", "-p${DB_PASSWORD}", "-e", "SELECT 1" ]
      interval: 20s
      timeout: 10s
      retries: 10
      start_period: 60s
    networks:
      - default

  mailpit:
      image: axllent/mailpit:v1.25
      restart: unless-stopped
      volumes:
          - mailpit:/data
      ports:
          - "2580:8025"
      expose:
          - 1025
      environment:
          MP_MAX_MESSAGES: 5000
          MP_DATABASE: /data/mailpit.db
          MP_SMTP_AUTH_ACCEPT_ANY: 1
          MP_SMTP_AUTH_ALLOW_INSECURE: 1
          MP_SENDMAIL_FROM: "<EMAIL>"
      networks:
          - default

volumes:
  db:
  php_caddy_data:
  php_caddy_config:
  mailpit:

networks:
  default:
