import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import {
    Dialog, DialogClose,
    DialogContent,
    DialogDescription, DialogFooter,
    DialogOverlay,
    DialogPortal,
    DialogTitle,
    DialogTrigger
} from '@/components/ui/dialog';
import Form from '@/pages/topics/form';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Topics',
        href: '/topics',
    },
];

export default function Topics() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Topics" />
            <div className="flex flex-row-reverse gap-4 rounded-xl p-4">
                <Dialog>
                    <DialogTrigger asChild>
                        <Button variant="default">
                            Create Topic
                        </Button>
                    </DialogTrigger>
                    <DialogContent>
                        <DialogTitle>Create Topic</DialogTitle>
                        <DialogDescription>
                            Tell us which topic you're interested in and when you'd like to receive the newsletter!
                        </DialogDescription>
                        <Form />
                        <DialogFooter>
                            <DialogClose asChild><Button variant="secondary">Cancel</Button></DialogClose>
                            <Button variant="default">Save</Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
