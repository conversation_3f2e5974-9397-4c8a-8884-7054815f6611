import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectTrigger, SelectContent, SelectItem } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

const weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
const weekdayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

function formatDate(dateStr: string) {
    const d = new Date(dateStr);
    return d.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: '2-digit' });
}

export default function RecurrenceForm() {
    const [startDate, setStartDate] = useState('');
    const [interval, setInterval] = useState(1);
    const [frequency, setFrequency] = useState('day');
    const [selectedDays, setSelectedDays] = useState<string[]>(['tuesday']);
    const [monthlyOption, setMonthlyOption] = useState('day');
    const [description, setDescription] = useState('');

    const toggleDay = (day: string) => {
        setSelectedDays(prev =>
            prev.includes(day) ? prev.filter(d => d !== day) : [...prev, day]
        );
    };

    useEffect(() => {
        if (!startDate) return;
        const date = new Date(startDate);
        let desc = `Repeats every ${interval} ${frequency}${interval > 1 ? 's' : ''}`;

        if (frequency === 'week') {
            const days = selectedDays.map(day => weekdays[weekdayKeys.indexOf(day)]).join(', ');
            desc += days ? ` on ${days}` : '';
        }

        if (frequency === 'month') {
            if (monthlyOption === 'day') {
                desc += ` on day ${date.getDate()} of the month`;
            } else if (monthlyOption === 'nthWeekday') {
                const day = weekdays[date.getDay()];
                const weekNum = Math.ceil(date.getDate() / 7);
                desc += ` on the ${weekNum}${['th','st','nd','rd'][weekNum] || 'th'} ${day} of the month`;
            } else {
                const day = weekdays[date.getDay()];
                desc += ` on the last ${day} of the month`;
            }
        }

        setDescription(desc);
    }, [startDate, interval, frequency, selectedDays, monthlyOption]);

    return (
        <Card className="max-w-xl mx-auto mt-6">
            <CardContent className="p-6 space-y-6">
                <form className="space-y-4">
                    <div>
                        <Label htmlFor="startDate">Start Date</Label>
                        <Input type="date" id="startDate" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
                    </div>

                    <div className="flex gap-4 items-end">
                        <div>
                            <Label>Repeat Every</Label>
                            <Input
                                type="number"
                                min={1}
                                value={interval}
                                onChange={(e) => setInterval(parseInt(e.target.value))}
                                className="w-20"
                            />
                        </div>
                        <div>
                            <Label>Frequency</Label>
                            <Select value={frequency} onValueChange={setFrequency}>
                                <SelectTrigger className="w-32 capitalize">{frequency}</SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="day">Daily</SelectItem>
                                    <SelectItem value="week">Weekly</SelectItem>
                                    <SelectItem value="month">Monthly</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {frequency === 'week' && (
                        <div>
                            <Label>Select Weekdays</Label>
                            <div className="flex gap-2 mt-2">
                                {weekdayKeys.map((day, idx) => (
                                    <Button
                                        key={day}
                                        type="button"
                                        variant={selectedDays.includes(day) ? 'default' : 'outline'}
                                        className="w-10 h-10 p-0 rounded-full"
                                        onClick={() => toggleDay(day)}
                                    >
                                        {weekdays[idx][0]}
                                    </Button>
                                ))}
                            </div>
                        </div>
                    )}

                    {frequency === 'month' && (
                        <div>
                            <Label>Monthly Options</Label>
                            <ToggleGroup type="single" value={monthlyOption} onValueChange={(val) => setMonthlyOption(val || 'day')} className="mt-2 gap-2">
                                <ToggleGroupItem value="day">On day {startDate && new Date(startDate).getDate()}</ToggleGroupItem>
                                <ToggleGroupItem value="nthWeekday">On the same weekday (e.g., 2nd Tuesday)</ToggleGroupItem>
                                <ToggleGroupItem value="lastWeekday">On the last weekday of the month</ToggleGroupItem>
                            </ToggleGroup>
                        </div>
                    )}

                    <div className="text-sm text-muted-foreground">
                        {description}
                    </div>
                </form>
            </CardContent>
        </Card>
    );
}
