FROM dunglas/frankenphp:1.5.0-php8.4-bookworm

LABEL version="0.0.1"
LABEL vendor="News My Way"
LABEL description="A News My Way PHP alkalmazás futtató környezete"

# Telepítjük a szükséges csomagokat és a Node.js-t
RUN apt-get -y update \
    && apt-get install -y --no-install-recommends git=1:2.39.5-0+deb12u2 zip=3.0-13 unzip=6.0-28 nano=7.2-1+deb12u1 supervisor=4.2.5-1 \
    ca-certificates curl gnupg \
    && mkdir -p /etc/apt/keyrings \
    && curl -fsSL https://deb.nodesource.com/gpgkey/nodesource-repo.gpg.key | gpg --dearmor -o /etc/apt/keyrings/nodesource.gpg \
    && echo "deb [signed-by=/etc/apt/keyrings/nodesource.gpg] https://deb.nodesource.com/node_22.x nodistro main" | tee /etc/apt/sources.list.d/nodesource.list \
    && apt-get update \
    && apt-get install -y nodejs \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* \
    && rm /etc/supervisor/supervisord.conf

# Composer és PHP beállítások
RUN mkdir -p /.cache/composer \
    && chmod -R 777 /.cache/composer \
    && mkdir -p /var/log/supervisor

#RUN pecl install redis-6.1.0 \
#    && docker-php-ext-enable redis

RUN install-php-extensions \
    pcntl \
    pdo_mysql \
    sockets
    # Add other PHP extensions here that cannot be specified with Composer (package.json) files...

# Konfigurációs fájlok másolása
COPY docker/php/supervisor/supervisord.conf /etc/supervisor/supervisord.conf
COPY docker/php/supervisor/frankenphp.conf /etc/supervisor/conf.d/frankenphp.conf
COPY docker/php/supervisor/vite.conf /etc/supervisor/conf.d/vite.conf
COPY docker/php/supervisor/queue.conf /etc/supervisor/conf.d/queue.conf
COPY docker/php/php.ini-development /usr/local/etc/php
COPY docker/php/Caddyfile /etc/caddy/

RUN cp "$PHP_INI_DIR/php.ini-development" "$PHP_INI_DIR/php.ini"

COPY --from=composer/composer:2.8.9 /usr/bin/composer /usr/local/bin/composer

# Adjunk hozzá egy entrypoint szkriptet
COPY docker/php/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf"]

EXPOSE 80
