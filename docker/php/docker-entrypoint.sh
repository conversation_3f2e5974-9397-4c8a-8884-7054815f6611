#!/bin/bash
set -e

# Ha a node_modules könyv<PERSON><PERSON><PERSON> ne<PERSON>, telep<PERSON><PERSON><PERSON>k a függőségeket
if [ ! -d /app/node_modules ]; then
    echo "Installing npm dependencies..."
    cd /app
    # Töröljük a package-lock.json fájlt, ha létezik
    if [ -f /app/package-lock.json ]; then
        rm /app/package-lock.json
    fi
    npm install
fi

# Adjuk át a vezérlést a megadott parancsnak
exec "$@"