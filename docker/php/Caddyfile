{
	{$CADDY_GLOBAL_OPTIONS}

	frankenphp {
		#worker /path/to/your/worker.php
		{$FRANKENPHP_CONFIG}
	}

	# https://caddyserver.com/docs/caddyfile/directives#sorting-algorithm
	order mercure after encode
	order vulcain after reverse_proxy
	order php_server before file_server
	order php before file_server
}

{$CADDY_EXTRA_CONFIG}

{$SERVER_NAME:localhost} {
	log {
		output stdout
    	format console
	}
	#log {
	#	# Redact the authorization query parameter that can be set by Mercure
	#	format filter {
	#		wrap console
	#		fields {
	#			uri query {
	#				replace authorization REDACTED
	#			}
	#		}
	#	}
	#}

	root * public/
	encode zstd br gzip

	# Uncomment the following lines to enable Mercure and Vulcain modules
	#mercure {
	#	# Transport to use (default to Bolt)
	#	transport_url {$MERCURE_TRANSPORT_URL:bolt:///data/mercure.db}
	#	# Publisher JWT key
	#	publisher_jwt {env.MERCURE_PUBLISHER_JWT_KEY} {env.MERCURE_PUBLISHER_JWT_ALG}
	#	# Subscriber JWT key
	#	subscriber_jwt {env.MERCURE_SUBSCRIBER_JWT_KEY} {env.MERCURE_SUBSCRIBER_JWT_ALG}
	#	# Allow anonymous subscribers (double-check that it's what you want)
	#	anonymous
	#	# Enable the subscription API (double-check that it's what you want)
	#	subscriptions
	#	# Extra directives
	#	{$MERCURE_EXTRA_DIRECTIVES}
	#}
	#vulcain

	{$CADDY_SERVER_EXTRA_DIRECTIVES}

	php_server
}
