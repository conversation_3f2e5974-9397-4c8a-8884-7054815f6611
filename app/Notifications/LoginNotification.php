<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use MagicLink\Actions\LoginAction;
use MagicLink\MagicLink;

class LoginNotification extends Notification implements ShouldQueue
{
    use Queueable;

    private const int EXPIRATION_MINUTES = 10;

    /**
     * Create a new notification instance.
     */
    public function __construct(private readonly User $user)
    {
        $this->queue = 'mail.login';
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)->subject('Your Magic Link to Sign In')->markdown('mail.login', [
            'magicLink' => $this->getMagicLink(),
            'expiresInMinutes' => self::EXPIRATION_MINUTES,
        ]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }

    private function getMagicLink(): string
    {
        $action = new LoginAction($this->user);
        $action->response(redirect(route('dashboard')));

       return MagicLink::create($action, self::EXPIRATION_MINUTES, 1)->url;
    }
}
