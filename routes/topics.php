<?php

use App\Http\Controllers\Topics\TopicsController;
use Illuminate\Support\Facades\Route;

Route::middleware('auth')->group(function () {
    Route::redirect('topics', 'topics/topics');

    Route::get('topics/topics', [TopicsController::class, 'index'])->name('topics.index');
//    Route::patch('settings/profile', [ProfileController::class, 'update'])->name('profile.update');
//    Route::delete('settings/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
//
//    Route::get('settings/appearance', function () {
//        return Inertia::render('settings/appearance');
//    })->name('appearance');
});
